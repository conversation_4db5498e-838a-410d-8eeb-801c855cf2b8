# OpenGL Draw Triangle

这是一个使用C++通过JNI调用OpenGL ES 2.0 API绘制三角形的Android应用程序。

## 功能特性

- 使用C++实现OpenGL ES 2.0渲染逻辑
- 通过JNI接口连接Java和C++代码
- 绘制一个彩色三角形（红、绿、蓝顶点）
- 支持多种Android架构（arm64-v8a, armeabi-v7a, x86, x86_64）

## 项目结构

### C++部分
- `app/src/main/cpp/gl_renderer.h` - OpenGL渲染器头文件
- `app/src/main/cpp/gl_renderer.cpp` - OpenGL渲染器实现
- `app/src/main/cpp/native-lib.cpp` - JNI接口实现
- `app/src/main/cpp/CMakeLists.txt` - CMake构建配置

### Java部分
- `MainActivity.java` - 主活动，设置GLSurfaceView
- `GLJNILib.java` - JNI接口声明
- `GLJNIView.java` - 自定义GLSurfaceView实现

## 构建说明

1. 确保已安装Android Studio和NDK
2. 打开项目
3. 运行以下命令构建APK：
   ```bash
   ./gradlew assembleDebug
   ```
4. APK文件将生成在：`app/build/outputs/apk/debug/app-debug.apk`

## 技术实现

### OpenGL ES 2.0特性
- 顶点着色器和片段着色器
- 顶点缓冲区对象
- 颜色插值

### 三角形规格
- 三个顶点：顶部（红色）、左下（绿色）、右下（蓝色）
- 使用标准化设备坐标
- 自动颜色插值

## 安装和运行

1. 将生成的APK安装到Android设备：
   ```bash
   adb install app/build/outputs/apk/debug/app-debug.apk
   ```
2. 在设备上启动应用
3. 您将看到一个彩色三角形显示在黑色背景上

## 系统要求

- Android API 24+
- 支持OpenGL ES 2.0的设备
- NDK r21或更高版本

## 开发环境

- Android Studio
- CMake 3.22.1+
- NDK
- Gradle 8.10.1

## 故障排除

如果遇到构建问题：
1. 确保NDK已正确安装
2. 检查CMakeLists.txt中的路径
3. 清理并重新构建项目：`./gradlew clean assembleDebug`

## 许可证

此项目仅用于学习和演示目的。
