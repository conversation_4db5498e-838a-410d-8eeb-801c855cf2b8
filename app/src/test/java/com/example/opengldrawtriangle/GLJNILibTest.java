package com.example.opengldrawtriangle;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Unit test for GLJNILib native methods.
 * 
 * Note: These tests verify the JNI interface exists but cannot test
 * OpenGL functionality without an OpenGL context.
 */
public class GLJNILibTest {
    
    @Test
    public void testNativeMethodsExist() {
        // This test verifies that the native methods are properly declared
        // The actual OpenGL functionality requires a valid OpenGL context
        // which is not available in unit tests
        
        try {
            // These calls will fail without OpenGL context, but we're just
            // checking that the methods exist and can be called
            GLJNILib.class.getDeclaredMethod("init");
            GLJNILib.class.getDeclaredMethod("resize", int.class, int.class);
            GLJNILib.class.getDeclaredMethod("step");
            GLJNILib.class.getDeclaredMethod("surfaceCreated");
            
            // If we get here, all methods exist
            assertTrue("All native methods are properly declared", true);
        } catch (NoSuchMethodException e) {
            fail("Native method not found: " + e.getMessage());
        }
    }
    
    @Test
    public void testMainActivityStringMethod() {
        // Test the string method from MainActivity
        MainActivity activity = new MainActivity();
        
        try {
            // This will fail without the native library loaded, but we're
            // just checking the method exists
            MainActivity.class.getDeclaredMethod("stringFromJNI");
            assertTrue("stringFromJNI method exists", true);
        } catch (NoSuchMethodException e) {
            fail("stringFromJNI method not found: " + e.getMessage());
        }
    }
}
