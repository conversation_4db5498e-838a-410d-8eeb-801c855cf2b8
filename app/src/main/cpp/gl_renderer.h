#ifndef OPENGLDRAWTRIANGLE_GL_RENDERER_H
#define OPENGLDRAWTRIANGLE_GL_RENDERER_H

#include <GLES2/gl2.h>
#include <GLES2/gl2ext.h>
#include <android/log.h>

#define LOG_TAG "OpenGLTriangle"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

class GLRenderer {
public:
    G<PERSON>enderer();
    ~G<PERSON>enderer();
    
    void surfaceCreated();
    void surfaceChanged(int width, int height);
    void drawFrame();
    
private:
    GLuint loadShader(GLenum shaderType, const char* shaderSource);
    GLuint createProgram(const char* vertexSource, const char* fragmentSource);
    void checkGLError(const char* operation);
    
    GLuint mProgram;
    GLuint mPositionHandle;
    GLuint mColorHandle;
    
    // Triangle vertices
    static const GLfloat triangleVertices[];
    static const GLfloat triangleColors[];
    
    // Shader sources
    static const char* vertexShaderSource;
    static const char* fragmentShaderSource;
};

#endif //OPENGLDRAWTRIANGLE_GL_RENDERER_H
