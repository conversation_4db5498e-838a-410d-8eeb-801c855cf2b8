#include "gl_renderer.h"
#include <cstdlib>

// Vertex shader source code
const char* GLRenderer::vertexShaderSource = 
    "attribute vec4 vPosition;\n"
    "attribute vec4 vColor;\n"
    "varying vec4 fColor;\n"
    "void main() {\n"
    "  gl_Position = vPosition;\n"
    "  fColor = vColor;\n"
    "}\n";

// Fragment shader source code
const char* GLRenderer::fragmentShaderSource = 
    "precision mediump float;\n"
    "varying vec4 fColor;\n"
    "void main() {\n"
    "  gl_FragColor = fColor;\n"
    "}\n";

// Triangle vertices (x, y, z)
const GLfloat GLRenderer::triangleVertices[] = {
     0.0f,  0.5f, 0.0f,   // Top vertex
    -0.5f, -0.5f, 0.0f,   // Bottom left vertex
     0.5f, -0.5f, 0.0f    // Bottom right vertex
};

// Triangle colors (r, g, b, a)
const GLfloat GLRenderer::triangleColors[] = {
    1.0f, 0.0f, 0.0f, 1.0f,   // Red
    0.0f, 1.0f, 0.0f, 1.0f,   // Green
    0.0f, 0.0f, 1.0f, 1.0f    // Blue
};

GLRenderer::GLRenderer() : mProgram(0), mPositionHandle(0), mColorHandle(0) {
}

GLRenderer::~GLRenderer() {
    if (mProgram) {
        glDeleteProgram(mProgram);
    }
}

void GLRenderer::surfaceCreated() {
    LOGI("Surface created");
    
    // Set the background color to black
    glClearColor(0.0f, 0.0f, 0.0f, 1.0f);
    
    // Create shader program
    mProgram = createProgram(vertexShaderSource, fragmentShaderSource);
    if (!mProgram) {
        LOGE("Could not create program");
        return;
    }
    
    // Get attribute locations
    mPositionHandle = glGetAttribLocation(mProgram, "vPosition");
    checkGLError("glGetAttribLocation vPosition");
    if (mPositionHandle == -1) {
        LOGE("Could not get attrib location for vPosition");
    }
    
    mColorHandle = glGetAttribLocation(mProgram, "vColor");
    checkGLError("glGetAttribLocation vColor");
    if (mColorHandle == -1) {
        LOGE("Could not get attrib location for vColor");
    }
}

void GLRenderer::surfaceChanged(int width, int height) {
    LOGI("Surface changed: %dx%d", width, height);
    glViewport(0, 0, width, height);
}

void GLRenderer::drawFrame() {
    // Clear the screen
    glClear(GL_COLOR_BUFFER_BIT);
    
    // Use our shader program
    glUseProgram(mProgram);
    checkGLError("glUseProgram");
    
    // Enable vertex attribute arrays
    glEnableVertexAttribArray(mPositionHandle);
    glEnableVertexAttribArray(mColorHandle);
    
    // Prepare the triangle coordinate data
    glVertexAttribPointer(mPositionHandle, 3, GL_FLOAT, GL_FALSE, 0, triangleVertices);
    checkGLError("glVertexAttribPointer position");
    
    // Prepare the triangle color data
    glVertexAttribPointer(mColorHandle, 4, GL_FLOAT, GL_FALSE, 0, triangleColors);
    checkGLError("glVertexAttribPointer color");
    
    // Draw the triangle
    glDrawArrays(GL_TRIANGLES, 0, 3);
    checkGLError("glDrawArrays");
    
    // Disable vertex attribute arrays
    glDisableVertexAttribArray(mPositionHandle);
    glDisableVertexAttribArray(mColorHandle);
}

GLuint GLRenderer::loadShader(GLenum shaderType, const char* shaderSource) {
    GLuint shader = glCreateShader(shaderType);
    if (shader) {
        glShaderSource(shader, 1, &shaderSource, NULL);
        glCompileShader(shader);
        GLint compiled = 0;
        glGetShaderiv(shader, GL_COMPILE_STATUS, &compiled);
        if (!compiled) {
            GLint infoLen = 0;
            glGetShaderiv(shader, GL_INFO_LOG_LENGTH, &infoLen);
            if (infoLen) {
                char* buf = (char*) malloc(infoLen);
                if (buf) {
                    glGetShaderInfoLog(shader, infoLen, NULL, buf);
                    LOGE("Could not compile shader %d:\n%s\n", shaderType, buf);
                    free(buf);
                }
                glDeleteShader(shader);
                shader = 0;
            }
        }
    }
    return shader;
}

GLuint GLRenderer::createProgram(const char* vertexSource, const char* fragmentSource) {
    GLuint vertexShader = loadShader(GL_VERTEX_SHADER, vertexSource);
    if (!vertexShader) {
        return 0;
    }
    
    GLuint fragmentShader = loadShader(GL_FRAGMENT_SHADER, fragmentSource);
    if (!fragmentShader) {
        return 0;
    }
    
    GLuint program = glCreateProgram();
    if (program) {
        glAttachShader(program, vertexShader);
        checkGLError("glAttachShader vertex");
        glAttachShader(program, fragmentShader);
        checkGLError("glAttachShader fragment");
        glLinkProgram(program);
        GLint linkStatus = GL_FALSE;
        glGetProgramiv(program, GL_LINK_STATUS, &linkStatus);
        if (linkStatus != GL_TRUE) {
            GLint bufLength = 0;
            glGetProgramiv(program, GL_INFO_LOG_LENGTH, &bufLength);
            if (bufLength) {
                char* buf = (char*) malloc(bufLength);
                if (buf) {
                    glGetProgramInfoLog(program, bufLength, NULL, buf);
                    LOGE("Could not link program:\n%s\n", buf);
                    free(buf);
                }
            }
            glDeleteProgram(program);
            program = 0;
        }
    }
    return program;
}

void GLRenderer::checkGLError(const char* operation) {
    for (GLint error = glGetError(); error; error = glGetError()) {
        LOGI("after %s() glError (0x%x)\n", operation, error);
    }
}
