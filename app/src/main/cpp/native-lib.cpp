#include <jni.h>
#include <string>
#include "gl_renderer.h"

static GLRenderer* g_renderer = nullptr;

extern "C" JNIEXPORT jstring JNICALL
Java_com_example_opengldrawtriangle_MainActivity_stringFromJNI(
        JNIEnv* env,
        jobject /* this */) {
    std::string hello = "Hello from C++ OpenGL Triangle!";
    return env->NewStringUTF(hello.c_str());
}

extern "C" JNIEXPORT void JNICALL
Java_com_example_opengldrawtriangle_GLJNILib_init(JNIEnv* env, jobject obj) {
    if (g_renderer) {
        delete g_renderer;
    }
    g_renderer = new GLRenderer();
}

extern "C" JNIEXPORT void JNICALL
Java_com_example_opengldrawtriangle_GLJNILib_resize(JNIEnv* env, jobject obj, jint width, jint height) {
    if (g_renderer) {
        g_renderer->surfaceChanged(width, height);
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_example_opengldrawtriangle_GLJNILib_step(JNIEnv* env, jobject obj) {
    if (g_renderer) {
        g_renderer->drawFrame();
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_example_opengldrawtriangle_GLJNILib_surfaceCreated(JNIEnv* env, jobject obj) {
    if (g_renderer) {
        g_renderer->surfaceCreated();
    }
}