package com.example.opengldrawtriangle;

public class GLJNILib {
    
    static {
        System.loadLibrary("opengldrawtriangle");
    }
    
    /**
     * Initialize the OpenGL renderer
     */
    public static native void init();
    
    /**
     * Called when the surface is resized
     */
    public static native void resize(int width, int height);
    
    /**
     * Called to render a frame
     */
    public static native void step();
    
    /**
     * Called when the surface is created
     */
    public static native void surfaceCreated();
}
