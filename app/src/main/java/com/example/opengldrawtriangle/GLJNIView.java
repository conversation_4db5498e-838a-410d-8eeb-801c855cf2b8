package com.example.opengldrawtriangle;

import android.content.Context;
import android.opengl.GLSurfaceView;
import android.util.AttributeSet;
import android.util.Log;

import javax.microedition.khronos.egl.EGLConfig;
import javax.microedition.khronos.opengles.GL10;

public class GLJNIView extends GLSurfaceView {
    private static final String TAG = "GLJNIView";
    private static final boolean DEBUG = false;

    public GLJNIView(Context context) {
        super(context);
        init(false, 0, 0);
    }

    public GLJNIView(Context context, boolean translucent, int depth, int stencil) {
        super(context);
        init(translucent, depth, stencil);
    }

    private void init(boolean translucent, int depth, int stencil) {
        /* By default, GLSurfaceView() creates a RGB_565 opaque surface.
         * If we want a translucent one, we should change the surface's
         * format here, using PixelFormat.TRANSLUCENT for GL Surfaces
         * is interpreted as any 32-bit surface with alpha by SurfaceFlinger.
         */
        if (translucent) {
            this.getHolder().setFormat(android.graphics.PixelFormat.TRANSLUCENT);
        }

        /* Setup the context factory for 2.0 rendering.
         * See ContextFactory class definition below
         */
        setEGLContextClientVersion(2);

        /* We need to choose an EGLConfig that matches the format of
         * our surface exactly. This is going to be done in our
         * custom config chooser. See ConfigChooser class definition
         * below.
         */
        setEGLConfigChooser(translucent ?
                new ConfigChooser(8, 8, 8, 8, depth, stencil) :
                new ConfigChooser(5, 6, 5, 0, depth, stencil));

        /* Set the renderer responsible for frame rendering */
        setRenderer(new Renderer());
    }

    private static class ConfigChooser implements GLSurfaceView.EGLConfigChooser {

        public ConfigChooser(int r, int g, int b, int a, int depth, int stencil) {
            mRedSize = r;
            mGreenSize = g;
            mBlueSize = b;
            mAlphaSize = a;
            mDepthSize = depth;
            mStencilSize = stencil;
        }

        /* This EGL config specification is used to specify 2.0 rendering.
         * We use a minimum size of 4 bits for red/green/blue, but will
         * perform actual matching in chooseConfig() below.
         */
        private static int EGL_OPENGL_ES2_BIT = 4;
        private static int[] s_configAttribs2 =
        {
            android.opengl.EGL14.EGL_RED_SIZE, 4,
            android.opengl.EGL14.EGL_GREEN_SIZE, 4,
            android.opengl.EGL14.EGL_BLUE_SIZE, 4,
            android.opengl.EGL14.EGL_RENDERABLE_TYPE, EGL_OPENGL_ES2_BIT,
            android.opengl.EGL14.EGL_NONE
        };

        public javax.microedition.khronos.egl.EGLConfig chooseConfig(javax.microedition.khronos.egl.EGL10 egl, javax.microedition.khronos.egl.EGLDisplay display) {

            /* Get the number of minimally matching EGL configurations
             */
            int[] num_config = new int[1];
            egl.eglChooseConfig(display, s_configAttribs2, null, 0, num_config);

            int numConfigs = num_config[0];

            if (numConfigs <= 0) {
                throw new IllegalArgumentException("No configs match configSpec");
            }

            /* Allocate then read the array of minimally matching EGL configs
             */
            javax.microedition.khronos.egl.EGLConfig[] configs = new javax.microedition.khronos.egl.EGLConfig[numConfigs];
            egl.eglChooseConfig(display, s_configAttribs2, configs, numConfigs, num_config);

            if (DEBUG) {
                 printConfigs(egl, display, configs);
            }
            /* Now return the "best" one
             */
            return chooseConfig(egl, display, configs);
        }

        public javax.microedition.khronos.egl.EGLConfig chooseConfig(javax.microedition.khronos.egl.EGL10 egl, javax.microedition.khronos.egl.EGLDisplay display,
                javax.microedition.khronos.egl.EGLConfig[] configs) {
            for(javax.microedition.khronos.egl.EGLConfig config : configs) {
                int d = findConfigAttrib(egl, display, config,
                        android.opengl.EGL14.EGL_DEPTH_SIZE, 0);
                int s = findConfigAttrib(egl, display, config,
                        android.opengl.EGL14.EGL_STENCIL_SIZE, 0);

                // We need at least mDepthSize and mStencilSize bits
                if (d < mDepthSize || s < mStencilSize)
                    continue;

                // We want an *exact* match for red/green/blue/alpha
                int r = findConfigAttrib(egl, display, config,
                        android.opengl.EGL14.EGL_RED_SIZE, 0);
                int g = findConfigAttrib(egl, display, config,
                            android.opengl.EGL14.EGL_GREEN_SIZE, 0);
                int b = findConfigAttrib(egl, display, config,
                            android.opengl.EGL14.EGL_BLUE_SIZE, 0);
                int a = findConfigAttrib(egl, display, config,
                        android.opengl.EGL14.EGL_ALPHA_SIZE, 0);

                if (r == mRedSize && g == mGreenSize && b == mBlueSize && a == mAlphaSize)
                    return config;
            }
            return null;
        }

        private int findConfigAttrib(javax.microedition.khronos.egl.EGL10 egl, javax.microedition.khronos.egl.EGLDisplay display,
                javax.microedition.khronos.egl.EGLConfig config, int attribute, int defaultValue) {

            if (egl.eglGetConfigAttrib(display, config, attribute, mValue)) {
                return mValue[0];
            }
            return defaultValue;
        }

        private void printConfigs(javax.microedition.khronos.egl.EGL10 egl, javax.microedition.khronos.egl.EGLDisplay display,
            javax.microedition.khronos.egl.EGLConfig[] configs) {
            int numConfigs = configs.length;
            Log.w(TAG, String.format("%d configurations", numConfigs));
            for (int i = 0; i < numConfigs; i++) {
                Log.w(TAG, String.format("Configuration %d:\n", i));
                printConfig(egl, display, configs[i]);
            }
        }

        private void printConfig(javax.microedition.khronos.egl.EGL10 egl, javax.microedition.khronos.egl.EGLDisplay display,
                javax.microedition.khronos.egl.EGLConfig config) {

            int[] attributes = {
                    android.opengl.EGL14.EGL_BUFFER_SIZE,
                    android.opengl.EGL14.EGL_ALPHA_SIZE,
                    android.opengl.EGL14.EGL_BLUE_SIZE,
                    android.opengl.EGL14.EGL_GREEN_SIZE,
                    android.opengl.EGL14.EGL_RED_SIZE,
                    android.opengl.EGL14.EGL_DEPTH_SIZE,
                    android.opengl.EGL14.EGL_STENCIL_SIZE,
                    android.opengl.EGL14.EGL_CONFIG_CAVEAT,
                    android.opengl.EGL14.EGL_CONFIG_ID,
                    android.opengl.EGL14.EGL_LEVEL,
                    android.opengl.EGL14.EGL_MAX_PBUFFER_HEIGHT,
                    android.opengl.EGL14.EGL_MAX_PBUFFER_PIXELS,
                    android.opengl.EGL14.EGL_MAX_PBUFFER_WIDTH,
                    android.opengl.EGL14.EGL_NATIVE_RENDERABLE,
                    android.opengl.EGL14.EGL_NATIVE_VISUAL_ID,
                    android.opengl.EGL14.EGL_NATIVE_VISUAL_TYPE,
                    0x3030, // EGL_PRESERVED_RESOURCES
                    android.opengl.EGL14.EGL_SAMPLES,
                    android.opengl.EGL14.EGL_SAMPLE_BUFFERS,
                    android.opengl.EGL14.EGL_SURFACE_TYPE,
                    android.opengl.EGL14.EGL_TRANSPARENT_TYPE,
                    android.opengl.EGL14.EGL_TRANSPARENT_RED_VALUE,
                    android.opengl.EGL14.EGL_TRANSPARENT_GREEN_VALUE,
                    android.opengl.EGL14.EGL_TRANSPARENT_BLUE_VALUE,
                    0x3039, // EGL_BIND_TO_TEXTURE_RGB
                    0x303A, // EGL_BIND_TO_TEXTURE_RGBA
                    0x303B, // EGL_MIN_SWAP_INTERVAL
                    0x303C, // EGL_MAX_SWAP_INTERVAL
                    android.opengl.EGL14.EGL_LUMINANCE_SIZE,
                    android.opengl.EGL14.EGL_ALPHA_MASK_SIZE,
                    android.opengl.EGL14.EGL_COLOR_BUFFER_TYPE,
                    android.opengl.EGL14.EGL_RENDERABLE_TYPE,
                    0x3042 // EGL_CONFORMANT
            };
            String[] names = {
                    "EGL_BUFFER_SIZE",
                    "EGL_ALPHA_SIZE",
                    "EGL_BLUE_SIZE",
                    "EGL_GREEN_SIZE",
                    "EGL_RED_SIZE",
                    "EGL_DEPTH_SIZE",
                    "EGL_STENCIL_SIZE",
                    "EGL_CONFIG_CAVEAT",
                    "EGL_CONFIG_ID",
                    "EGL_LEVEL",
                    "EGL_MAX_PBUFFER_HEIGHT",
                    "EGL_MAX_PBUFFER_PIXELS",
                    "EGL_MAX_PBUFFER_WIDTH",
                    "EGL_NATIVE_RENDERABLE",
                    "EGL_NATIVE_VISUAL_ID",
                    "EGL_NATIVE_VISUAL_TYPE",
                    "EGL_PRESERVED_RESOURCES",
                    "EGL_SAMPLES",
                    "EGL_SAMPLE_BUFFERS",
                    "EGL_SURFACE_TYPE",
                    "EGL_TRANSPARENT_TYPE",
                    "EGL_TRANSPARENT_RED_VALUE",
                    "EGL_TRANSPARENT_GREEN_VALUE",
                    "EGL_TRANSPARENT_BLUE_VALUE",
                    "EGL_BIND_TO_TEXTURE_RGB",
                    "EGL_BIND_TO_TEXTURE_RGBA",
                    "EGL_MIN_SWAP_INTERVAL",
                    "EGL_MAX_SWAP_INTERVAL",
                    "EGL_LUMINANCE_SIZE",
                    "EGL_ALPHA_MASK_SIZE",
                    "EGL_COLOR_BUFFER_TYPE",
                    "EGL_RENDERABLE_TYPE",
                    "EGL_CONFORMANT"
            };
            int[] value = new int[1];
            for (int i = 0; i < attributes.length; i++) {
                int attribute = attributes[i];
                String name = names[i];
                if ( egl.eglGetConfigAttrib(display, config, attribute, value)) {
                    Log.w(TAG, String.format("  %s: %d\n", name, value[0]));
                } else {
                    // Log.w(TAG, String.format("  %s: failed\n", name));
                    while (egl.eglGetError() != javax.microedition.khronos.egl.EGL10.EGL_SUCCESS);
                }
            }
        }

        // Subclasses can adjust these values:
        protected int mRedSize;
        protected int mGreenSize;
        protected int mBlueSize;
        protected int mAlphaSize;
        protected int mDepthSize;
        protected int mStencilSize;
        private int[] mValue = new int[1];
    }

    private static class Renderer implements GLSurfaceView.Renderer {
        public void onDrawFrame(GL10 gl) {
            GLJNILib.step();
        }

        public void onSurfaceChanged(GL10 gl, int width, int height) {
            GLJNILib.resize(width, height);
        }

        public void onSurfaceCreated(GL10 gl, EGLConfig config) {
            GLJNILib.init();
            GLJNILib.surfaceCreated();
        }
    }
}
